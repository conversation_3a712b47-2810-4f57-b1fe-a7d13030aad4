-- Create tables
CREATE TABLE file_data (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    original_file_path VARCHAR(255) NOT NULL,
    content CLOB NOT NULL,
    file_size BIGINT NOT NULL,
    last_modified BIGINT NOT NULL,
    received_at TIMESTAMP NOT NULL
);

CREATE TABLE user_sessions (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    username VARCHAR(255) NOT NULL,
    connected_at TIMESTAMP NOT NULL,
    last_activity TIMESTAMP NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE
);

-- Create indexes
CREATE INDEX idx_file_received_at ON file_data(received_at);
CREATE INDEX idx_file_path ON file_data(original_file_path);
CREATE INDEX idx_session_id ON user_sessions(session_id);
CREATE INDEX idx_username ON user_sessions(username);
CREATE INDEX idx_session_active ON user_sessions(is_active);
