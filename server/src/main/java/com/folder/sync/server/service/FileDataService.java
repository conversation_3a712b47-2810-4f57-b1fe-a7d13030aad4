package com.folder.sync.server.service;

import com.folder.sync.server.domain.model.FileData;
import com.folder.sync.server.domain.repository.FileDataRepository;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class FileDataService {

    private final FileDataRepository fileDataRepository;
    
    @Value("${file.output-dir}")
    private String outputDir;

    @Value("${file.max-file-size}")
    private long maxFileSize;

    public FileDataService(FileDataRepository fileDataRepository) {
        this.fileDataRepository = fileDataRepository;
    }

    @Transactional
    public void processFileData(String filePath, String content, long fileSize, long lastModified) {
        // Validate file size
        if (fileSize > maxFileSize) {
            throw new IllegalArgumentException("File size exceeds maximum allowed size");
        }

        // Save to database
        FileData fileData = new FileData();
        fileData.setOriginalFilePath(filePath);
        fileData.setContent(content);
        fileData.setFileSize(fileSize);
        fileData.setLastModified(lastModified);
        fileDataRepository.save(fileData);

        // Write to file
        try {
            writeToFile(filePath, content);
        } catch (IOException e) {
            throw new RuntimeException("Failed to write file to disk", e);
        }
    }

    private void writeToFile(String originalFilePath, String content) throws IOException {
        // Create output directory if it doesn't exist
        Path outputDirPath = Paths.get(outputDir);
        Files.createDirectories(outputDirPath);

        // Generate unique filename based on original path and timestamp
        String fileName = UUID.randomUUID() + "_" + 
                          originalFilePath.replaceAll("\\\\", "_").replaceAll("/", "_") + 
                          "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".txt";
        
        Path outputPath = outputDirPath.resolve(fileName);

        // Write content to file
        try (BufferedWriter writer = Files.newBufferedWriter(outputPath)) {
            writer.write(content);
        }
    }

    /**
     * Gets the list of all file names that the server has received
     *
     * @return List of original file paths (file names) that the server has
     */
    public List<String> getExistingFileNames() {
        return fileDataRepository.findAll()
                .stream()
                .map(FileData::getOriginalFilePath)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * Gets the count of files the server has
     *
     * @return Total number of unique files
     */
    public long getFileCount() {
        return fileDataRepository.count();
    }
}
