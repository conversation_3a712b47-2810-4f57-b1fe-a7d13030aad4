package com.folder.sync.server.websocket;

import com.folder.sync.server.domain.model.FileEvent;
import com.folder.sync.server.domain.model.FileProcessingResult;
import com.folder.sync.server.domain.model.ServerFileListMessage;
import com.folder.sync.server.service.FileDataService;
import com.folder.sync.server.service.UserSessionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Controller;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

import java.util.List;

@Controller
public class FileSyncWebSocketController {
    private static final Logger log = LoggerFactory.getLogger(FileSyncWebSocketController.class);

    private final FileDataService fileDataService;
    private final SimpMessagingTemplate messagingTemplate;
    private final UserSessionService userSessionService;

    @Autowired
    public FileSyncWebSocketController(FileDataService fileDataService,
                                     SimpMessagingTemplate messagingTemplate,
                                     UserSessionService userSessionService) {
        this.fileDataService = fileDataService;
        this.messagingTemplate = messagingTemplate;
        this.userSessionService = userSessionService;
    }

    @MessageMapping("/sync")
    public void handleFileSync(@Payload FileProcessingResult result, SimpMessageHeaderAccessor headerAccessor) {
        String sessionId = headerAccessor.getSessionId();
        String username = getUsernameFromSession(headerAccessor);

        log.info("Received file sync message from user: {} (session: {}) for file: {}",
                username, sessionId, result.getData() != null ? result.getData().getFilePath() : "null");

        // Update session activity
        if (sessionId != null) {
            userSessionService.updateSessionActivity(sessionId);
        }

        if (result.isSuccess() && result.getData() != null) {
            try {
                // Process the file data
                FileEvent fileEvent = result.getData();

                fileDataService.processFileData(
                    fileEvent.getFilePath(),
                    fileEvent.getContent(),
                    fileEvent.getFileSize(),
                    fileEvent.getLastModified()
                );
                log.info("Successfully processed file: {} from user: {}", fileEvent.getFilePath(), username);
            } catch (Exception e) {
                log.error("Error processing file sync message from user {}: {}", username, e.getMessage(), e);
            }
        } else {
            log.warn("Received unsuccessful file processing result from user {}: {}", username, result.getMessage());
        }
    }

    /**
     * Handles WebSocket connection events and sends the file list to newly connected clients
     */
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        StompHeaderAccessor accessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = accessor.getSessionId();
        String username = getUsernameFromAccessor(accessor);

        log.info("New WebSocket connection established for user: {} (session: {})", username, sessionId);

        // Create user session
        if (sessionId != null && username != null) {
            userSessionService.createSession(sessionId, username);
        }

        try {
            // Get the list of existing files from the server
            List<String> existingFiles = fileDataService.getExistingFileNames();

            // Create the message
            ServerFileListMessage fileListMessage = new ServerFileListMessage(
                existingFiles,
                String.format("Server has %d files", existingFiles.size())
            );

            // Send to the client via the file list topic
            messagingTemplate.convertAndSend("/topic/filelist", fileListMessage);

            log.info("Sent file list with {} files to user: {} (session: {})",
                    existingFiles.size(), username, sessionId);

        } catch (Exception e) {
            log.error("Error sending file list to user {}: {}", username, e.getMessage(), e);
        }
    }

    /**
     * Handles WebSocket disconnection events
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor accessor = StompHeaderAccessor.wrap(event.getMessage());
        String sessionId = accessor.getSessionId();
        String username = getUsernameFromAccessor(accessor);

        log.info("WebSocket connection closed for user: {} (session: {})", username, sessionId);

        // Mark session as disconnected
        if (sessionId != null) {
            userSessionService.disconnectSession(sessionId);
        }
    }

    /**
     * Helper method to extract username from session header accessor
     */
    private String getUsernameFromSession(SimpMessageHeaderAccessor headerAccessor) {
        if (headerAccessor.getUser() != null) {
            return headerAccessor.getUser().getName();
        }

        // Fallback: try to get from session service
        String sessionId = headerAccessor.getSessionId();
        if (sessionId != null) {
            return userSessionService.getUsernameBySessionId(sessionId).orElse("unknown");
        }

        return "unknown";
    }

    /**
     * Helper method to extract username from STOMP header accessor
     */
    private String getUsernameFromAccessor(StompHeaderAccessor accessor) {
        if (accessor.getUser() != null) {
            return accessor.getUser().getName();
        }

        // Fallback: try to get from native headers
        String username = accessor.getFirstNativeHeader("user");
        if (username != null && !username.trim().isEmpty()) {
            return username.trim();
        }

        return "unknown";
    }
}
