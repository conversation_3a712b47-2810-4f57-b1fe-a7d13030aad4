package com.folder.sync.server.controller;

import com.folder.sync.server.domain.model.FileData;
import com.folder.sync.server.domain.repository.FileDataRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/v1/file-sync")
public class FileSyncController {

    private final FileDataRepository fileDataRepository;

    @Autowired
    public FileSyncController(FileDataRepository fileDataRepository) {
        this.fileDataRepository = fileDataRepository;
    }

    @GetMapping("/stats")
    public ResponseEntity<FileSyncStats> getStats() {
        long totalFiles = fileDataRepository.count();
        return ResponseEntity.ok(new FileSyncStats(totalFiles));
    }

    @GetMapping("/recent")
    public ResponseEntity<List<FileData>> getRecentFiles(@RequestParam(defaultValue = "10") int limit) {
        List<FileData> recentFiles = fileDataRepository.findTopByOrderByReceivedAtDesc(limit);
        return ResponseEntity.ok(recentFiles);
    }
}

record FileSyncStats(long totalFiles) {}
