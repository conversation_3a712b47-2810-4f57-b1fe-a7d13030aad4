# Java Folder Sync

A real-time file synchronization system built with Java and WebSockets.

## Features

- Real-time file synchronization between client and server
- WebSocket-based communication
- Configurable monitoring of input directories
- Automatic retry mechanism for failed transfers
- File size limitations and stability checks

## Prerequisites

- Java 17 or higher
- Maven 3.6.3 or higher

## Configuration

### Server

- Configure the server port and WebSocket endpoint in `server/src/main/resources/application.properties`

### Client

- Configure WebSocket URL and monitoring directory in `client/src/main/resources/application.properties`
  - Default input directory: `/var/folder_sync_input`
  - Can be overridden using `FILE_INPUT_DIR` environment variable

## Building

```bash
# Build both client and server
mvn clean install

# Build server only
cd server
mvn clean install

# Build client only
cd client
mvn clean install
```

## Running

### Server
```bash
cd server
mvn spring-boot:run
```

### Client
```bash
cd client
mvn spring-boot:run
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
